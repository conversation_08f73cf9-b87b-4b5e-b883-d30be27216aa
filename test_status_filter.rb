#!/usr/bin/env ruby

# Test script to verify the status filter functionality

puts "Testing status filter functionality..."

# Get a faculty user
faculty_user = User.joins(:teacher_enrollments).where(enrollments: { workflow_state: 'active' }).first

if faculty_user.nil?
  puts "No faculty user found!"
  exit 1
end

puts "Faculty user: #{faculty_user.name} (ID: #{faculty_user.id})"

# Get consultation requests by status
all_requests = faculty_user.faculty_consultation_requests
pending_requests = all_requests.where(status: 'pending')
approved_requests = all_requests.where(status: 'approved')
completed_summaries = faculty_user.faculty_consultation_summaries

puts "\n=== Current Data ==="
puts "Total requests: #{all_requests.count}"
puts "Pending requests: #{pending_requests.count}"
puts "Approved requests: #{approved_requests.count}"
puts "Completed summaries: #{completed_summaries.count}"

# Test the filter logic
puts "\n=== Testing Filter Logic ==="

# Test 1: Filter for pending only
puts "\n1. Testing status='pending' filter:"
summaries = faculty_user.faculty_consultation_summaries.preload(:student, :consultation_request)
pending_reqs = faculty_user.faculty_consultation_requests
                           .where(status: ['pending', 'approved'])
                           .preload(:student, :faculty_time_slot)

# Apply status filter for pending
pending_reqs = pending_reqs.where(status: 'pending')
summaries = summaries.none

puts "   Filtered summaries: #{summaries.count}"
puts "   Filtered pending requests: #{pending_reqs.count}"

# Test 2: Filter for approved only
puts "\n2. Testing status='approved' filter:"
summaries = faculty_user.faculty_consultation_summaries.preload(:student, :consultation_request)
approved_reqs = faculty_user.faculty_consultation_requests
                            .where(status: ['pending', 'approved'])
                            .preload(:student, :faculty_time_slot)

# Apply status filter for approved
approved_reqs = approved_reqs.where(status: 'approved')
summaries = summaries.none

puts "   Filtered summaries: #{summaries.count}"
puts "   Filtered approved requests: #{approved_reqs.count}"

# Test 3: Filter for completed only
puts "\n3. Testing status='completed' filter:"
summaries = faculty_user.faculty_consultation_summaries.preload(:student, :consultation_request)
completed_reqs = faculty_user.faculty_consultation_requests
                             .where(status: ['pending', 'approved'])
                             .preload(:student, :faculty_time_slot)

# Apply status filter for completed
completed_reqs = completed_reqs.none

puts "   Filtered summaries: #{summaries.count}"
puts "   Filtered pending requests: #{completed_reqs.count}"

puts "\n=== Test Complete ==="
