import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Badge } from '@instructure/ui-badge'
import { TextArea } from '@instructure/ui-text-area'
import { Modal } from '@instructure/ui-modal'
import { IconEditLine, IconUserLine, IconCalendarMonthLine, IconNoteLine } from '@instructure/ui-icons'
import { updateConsultationSummary, addNotesToSummary } from '../services/consultationSummariesApi'
import type { ConsultationSummary, ConsultationRequest } from '../types'

interface SummariesListProps {
  summaries: ConsultationSummary[]
  pendingRequests: ConsultationRequest[]
  loading: boolean
  onRefresh: () => void
}

const SummariesList: React.FC<SummariesListProps> = ({
  summaries,
  pendingRequests,
  loading,
  onRefresh
}) => {
  const [selectedSummary, setSelectedSummary] = useState<ConsultationSummary | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showNotesModal, setShowNotesModal] = useState(false)
  const [editData, setEditData] = useState({
    outcome_summary: '',
    referral_made: '',
    follow_up_required: ''
  })
  const [newNotes, setNewNotes] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const handleOpenEditModal = (summary: ConsultationSummary) => {
    setSelectedSummary(summary)
    setEditData({
      outcome_summary: summary.outcome_summary || '',
      referral_made: summary.referral_made || '',
      follow_up_required: summary.follow_up_required || ''
    })
    setShowEditModal(true)
  }

  const handleOpenNotesModal = (summary: ConsultationSummary) => {
    setSelectedSummary(summary)
    setNewNotes('')
    setShowNotesModal(true)
  }

  const handleCloseModals = () => {
    setSelectedSummary(null)
    setShowEditModal(false)
    setShowNotesModal(false)
    setEditData({ outcome_summary: '', referral_made: '', follow_up_required: '' })
    setNewNotes('')
  }

  const handleUpdateSummary = async () => {
    if (!selectedSummary) return

    try {
      setSubmitting(true)
      await updateConsultationSummary(selectedSummary.id, editData)
      onRefresh()
      handleCloseModals()
    } catch (error) {
      console.error('Error updating summary:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const handleAddNotes = async () => {
    if (!selectedSummary || !newNotes.trim()) return

    try {
      setSubmitting(true)
      await addNotesToSummary(selectedSummary.id, newNotes)
      onRefresh()
      handleCloseModals()
    } catch (error) {
      console.error('Error adding notes:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateString
    }
  }

  const totalItems = summaries.length + pendingRequests.length

  if (totalItems === 0) {
    return (
      <View as="div" textAlign="center" padding="x-large">
        <div className="empty-state">
          <div className="empty-icon">
            <IconUserLine size="large" />
          </div>
          <Heading level="h3" margin="0 0 small 0">
            No Consultations Found
          </Heading>
          <Text>
            No consultations or summaries match your current filters.
            Try adjusting your search criteria or check if there are any consultation requests.
          </Text>
        </div>
      </View>
    )
  }

  const renderPendingRequest = (request: ConsultationRequest) => (
    <View
      key={`request-${request.id}`}
      as="div"
      background="secondary"
      padding="medium"
      borderRadius="medium"
      borderWidth="small"
      borderColor="warning"
      margin="0 0 medium 0"
    >
      <View as="div" display="flex" justifyItems="space-between" alignItems="start">
        <View as="div" width="75%">
          <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
            <IconUserLine size="small" />
            <Heading level="h4" margin="0 0 0 x-small">
              {request.student_name}
            </Heading>
            <Text size="small" color="secondary" margin="0 0 0 small">
              ({request.student_id})
            </Text>
            <Badge
              type="notification"
              variant={request.status === 'pending' ? 'warning' : 'success'}
              margin="0 0 0 small"
            >
              {request.status_display}
            </Badge>
          </View>

          <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
            <IconCalendarMonthLine size="small" />
            <Text size="small" margin="0 0 0 x-small">
              {formatDate(request.consultation_date)} • {request.concern_type_display}
            </Text>
          </View>

          <Text size="small" color="secondary" margin="0 0 small 0">
            {request.description}
          </Text>

          {request.faculty_notes && (
            <View as="div" margin="small 0 0 0">
              <Text size="small" weight="bold">Faculty Notes:</Text>
              <Text size="small" margin="x-small 0 0 0">
                {request.faculty_notes}
              </Text>
            </View>
          )}
        </View>

        <View as="div" width="25%" textAlign="end">
          <Text size="x-small" color="secondary">
            {request.status === 'pending' ? 'Awaiting Approval' : 'Ready for Consultation'}
          </Text>
          {request.can_be_completed && (
            <View as="div" margin="small 0 0 0">
              <Button size="small" color="primary">
                Mark Complete
              </Button>
            </View>
          )}
        </View>
      </View>
    </View>
  )

  return (
    <>
      <View as="div" margin="medium 0 0 0">
        {/* Show pending/approved requests first */}
        {pendingRequests.length > 0 && (
          <View as="div" margin="0 0 large 0">
            <Heading level="h3" margin="0 0 medium 0">
              Pending & Approved Consultations ({pendingRequests.length})
            </Heading>
            {pendingRequests.map(renderPendingRequest)}
          </View>
        )}

        {/* Show completed summaries */}
        {summaries.length > 0 && (
          <View as="div">
            <Heading level="h3" margin="0 0 medium 0">
              Completed Consultations ({summaries.length})
            </Heading>
            {summaries.map(summary => (
          <View
            key={summary.id}
            as="div"
            background="primary"
            padding="medium"
            borderRadius="medium"
            borderWidth="small"
            borderColor="brand"
            margin="0 0 medium 0"
          >
            <View as="div" display="flex" justifyItems="space-between" alignItems="start">
              <View as="div" width="75%">
                <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                  <IconUserLine size="small" />
                  <Heading level="h4" margin="0 0 0 x-small">
                    {summary.student_name}
                  </Heading>
                  <Text size="small" color="secondary" margin="0 0 0 small">
                    ({summary.student_id})
                  </Text>
                  <Badge
                    type="notification"
                    variant="success"
                    margin="0 0 0 small"
                  >
                    Completed
                  </Badge>
                </View>

                <View as="div" margin="0 0 small 0">
                  <View as="div" display="flex" alignItems="center" margin="0 0 x-small 0">
                    <IconCalendarMonthLine size="x-small" />
                    <Text weight="bold" margin="0 0 0 x-small">
                      {formatDate(summary.consultation_date)}
                    </Text>
                  </View>
                </View>

                <View as="div" margin="0 0 small 0">
                  <Text size="small" weight="bold" color="secondary">
                    {summary.concern_type_display} • Completed
                    {summary.has_referral && ' • Referral Made'}
                    {summary.requires_follow_up && ' • Follow-up Required'}
                  </Text>
                </View>

                {summary.description && (
                  <View as="div" margin="small 0 0 0" background="secondary" padding="small" borderRadius="small">
                    <Text size="small" weight="bold" display="block" margin="0 0 x-small 0">
                      Original Concern:
                    </Text>
                    <Text size="small">
                      {summary.description}
                    </Text>
                  </View>
                )}

                {summary.outcome_summary && (
                  <View as="div" margin="small 0 0 0" background="success" padding="small" borderRadius="small">
                    <Text size="small" weight="bold" display="block" margin="0 0 x-small 0">
                      Outcome Summary:
                    </Text>
                    <Text size="small">
                      {summary.outcome_summary}
                    </Text>
                  </View>
                )}

                {summary.referral_made && (
                  <View as="div" margin="small 0 0 0" background="brand" padding="small" borderRadius="small">
                    <Text size="small" weight="bold" display="block" margin="0 0 x-small 0" color="primary-inverse">
                      Referral Made:
                    </Text>
                    <Text size="small" color="primary-inverse">
                      {summary.referral_made}
                    </Text>
                  </View>
                )}

                {summary.follow_up_required && (
                  <View as="div" margin="small 0 0 0" background="warning" padding="small" borderRadius="small">
                    <Text size="small" weight="bold" display="block" margin="0 0 x-small 0">
                      Follow-up Required:
                    </Text>
                    <Text size="small">
                      {summary.follow_up_required}
                    </Text>
                  </View>
                )}

                {summary.faculty_notes && (
                  <View as="div" margin="small 0 0 0" background="secondary" padding="small" borderRadius="small">
                    <Text size="small" weight="bold" display="block" margin="0 0 x-small 0">
                      Faculty Notes:
                    </Text>
                    <Text size="small" style={{ whiteSpace: 'pre-wrap' }}>
                      {summary.faculty_notes}
                    </Text>
                  </View>
                )}

                <View as="div" margin="small 0 0 0">
                  <Text size="x-small" color="secondary">
                    Duration: {summary.duration_display} • 
                    Created: {new Date(summary.created_at).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </Text>
                </View>
              </View>

              <View as="div" display="flex" direction="column" gap="small">
                <Button
                  size="small"
                  renderIcon={IconEditLine}
                  onClick={() => handleOpenEditModal(summary)}
                  disabled={loading}
                >
                  Edit
                </Button>
                <Button
                  size="small"
                  renderIcon={IconNoteLine}
                  onClick={() => handleOpenNotesModal(summary)}
                  disabled={loading}
                >
                  Add Notes
                </Button>
              </View>
            </View>
          </View>
            ))}
          </View>
        )}
      </View>

      {/* Edit Summary Modal */}
      <Modal
        open={showEditModal}
        onDismiss={handleCloseModals}
        size="large"
        label="Edit Consultation Summary"
      >
        <Modal.Header>
          <Heading level="h2">Edit Consultation Summary</Heading>
        </Modal.Header>
        
        <Modal.Body>
          {selectedSummary && (
            <View as="div">
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Student:</Text> {selectedSummary.student_name} ({selectedSummary.student_id})
              </View>
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Consultation Date:</Text> {formatDate(selectedSummary.consultation_date)}
              </View>
              
              <TextArea
                label="Outcome Summary"
                placeholder="Describe the outcome of the consultation..."
                value={editData.outcome_summary}
                onChange={(e) => setEditData(prev => ({ ...prev, outcome_summary: e.target.value }))}
                height="6rem"
              />
              
              <TextArea
                label="Referral Made"
                placeholder="If you made any referrals, describe them here..."
                value={editData.referral_made}
                onChange={(e) => setEditData(prev => ({ ...prev, referral_made: e.target.value }))}
                height="4rem"
                margin="medium 0 0 0"
              />
              
              <TextArea
                label="Follow-up Required"
                placeholder="Describe any follow-up actions needed..."
                value={editData.follow_up_required}
                onChange={(e) => setEditData(prev => ({ ...prev, follow_up_required: e.target.value }))}
                height="4rem"
                margin="medium 0 0 0"
              />
            </View>
          )}
        </Modal.Body>
        
        <Modal.Footer>
          <Button onClick={handleCloseModals} disabled={submitting}>
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={handleUpdateSummary}
            disabled={submitting}
            margin="0 0 0 x-small"
          >
            {submitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Add Notes Modal */}
      <Modal
        open={showNotesModal}
        onDismiss={handleCloseModals}
        size="medium"
        label="Add Notes to Consultation Summary"
      >
        <Modal.Header>
          <Heading level="h2">Add Notes</Heading>
        </Modal.Header>
        
        <Modal.Body>
          {selectedSummary && (
            <View as="div">
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Student:</Text> {selectedSummary.student_name} ({selectedSummary.student_id})
              </View>
              
              <TextArea
                label="Additional Notes"
                placeholder="Add additional notes about this consultation..."
                value={newNotes}
                onChange={(e) => setNewNotes(e.target.value)}
                height="8rem"
              />
              
              <View as="div" margin="medium 0 0 0" background="secondary" padding="small" borderRadius="small">
                <Text size="small">
                  <strong>Note:</strong> These notes will be appended to the existing faculty notes 
                  with a timestamp.
                </Text>
              </View>
            </View>
          )}
        </Modal.Body>
        
        <Modal.Footer>
          <Button onClick={handleCloseModals} disabled={submitting}>
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={handleAddNotes}
            disabled={submitting || !newNotes.trim()}
            margin="0 0 0 x-small"
          >
            {submitting ? 'Adding...' : 'Add Notes'}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  )
}

export default SummariesList
