#!/usr/bin/env ruby

# Script to create a test consultation summary
# This will help us test the consultation summaries API

# Get the first consultation request
request = ConsultationRequest.first
if request.nil?
  puts "No consultation requests found"
  exit 1
end

# Get faculty and student users
faculty = request.faculty
student = request.student

puts "Creating consultation summary for:"
puts "Faculty: #{faculty.name}"
puts "Student: #{student.name}"
puts "Request ID: #{request.id}"

# Create a consultation summary manually
summary = ConsultationSummary.create!(
  consultation_request: request,
  faculty: faculty,
  student: student,
  student_name: request.student_name,
  student_number: request.student_id,
  consultation_date: 1.day.ago,
  concern_type: request.nature_of_concern,
  description: request.description,
  faculty_notes: "Initial consultation completed successfully",
  outcome_summary: "Student received guidance on #{request.nature_of_concern.downcase} concerns",
  referral_made: "Referred to counseling services",
  follow_up_required: "Follow-up in 2 weeks"
)

puts "Created consultation summary with ID: #{summary.id}"
puts "Total consultation summaries: #{ConsultationSummary.count}"
