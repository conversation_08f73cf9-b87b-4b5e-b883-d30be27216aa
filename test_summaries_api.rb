#!/usr/bin/env ruby

# Test script to check the consultation summaries API

puts "Testing consultation summaries API..."

# Check current data
puts "\n=== Current Data ==="
puts "Consultation Requests: #{ConsultationRequest.count}"
puts "Consultation Summaries: #{ConsultationSummary.count}"

# Get a faculty user
faculty_user = User.joins(:teacher_enrollments).where(enrollments: { workflow_state: 'active' }).first

if faculty_user.nil?
  puts "No faculty user found!"
  exit 1
end

puts "Faculty user: #{faculty_user.name} (ID: #{faculty_user.id})"

# Check faculty's consultation requests
faculty_requests = faculty_user.faculty_consultation_requests
puts "Faculty's consultation requests: #{faculty_requests.count}"

pending_approved = faculty_requests.where(status: ['pending', 'approved'])
puts "Pending/Approved requests: #{pending_approved.count}"

completed_summaries = faculty_user.faculty_consultation_summaries
puts "Completed summaries: #{completed_summaries.count}"

# Test the controller logic manually
puts "\n=== Testing Controller Logic ==="

# Simulate what the controller does
summaries = faculty_user.faculty_consultation_summaries.preload(:student, :consultation_request)
                        .order(consultation_date: :desc)

pending_requests = faculty_user.faculty_consultation_requests
                              .where(status: ['pending', 'approved'])
                              .preload(:student, :faculty_time_slot)
                              .order(preferred_datetime: :desc)

puts "Summaries found: #{summaries.count}"
puts "Pending requests found: #{pending_requests.count}"

if pending_requests.any?
  puts "\nPending requests details:"
  pending_requests.each do |req|
    puts "- ID: #{req.id}, Student: #{req.student_name}, Status: #{req.status}, Date: #{req.preferred_datetime}"
  end
end

if summaries.any?
  puts "\nSummaries details:"
  summaries.each do |summary|
    puts "- ID: #{summary.id}, Student: #{summary.student_name}, Date: #{summary.consultation_date}"
  end
end

puts "\n=== Test Complete ==="
